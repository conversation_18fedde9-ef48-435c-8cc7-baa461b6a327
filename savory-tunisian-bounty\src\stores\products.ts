import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { supabase } from "@/lib/supabase";
import type { Database } from "@/lib/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];
type Category = Database["public"]["Tables"]["categories"]["Row"];

export const useProductsStore = defineStore("products", () => {
  const products = ref<Product[]>([]);
  const categories = ref<Category[]>([]);
  const loading = ref(false);
  const selectedCategory = ref<string | null>(null);
  const searchQuery = ref("");

  // Computed properties
  const filteredProducts = computed(() => {
    let filtered = products.value;

    // Filter by category
    if (selectedCategory.value) {
      filtered = filtered.filter(
        (product) => product.category_id === selectedCategory.value
      );
    }

    // Filter by search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(query) ||
          product.description?.toLowerCase().includes(query)
      );
    }

    return filtered;
  });

  const productsByCategory = computed(() => {
    const grouped: Record<string, Product[]> = {};

    categories.value.forEach((category) => {
      grouped[category.id] = products.value.filter(
        (product) => product.category_id === category.id
      );
    });

    return grouped;
  });

  // Actions
  const fetchProducts = async () => {
    try {
      loading.value = true;

      const { data, error } = await supabase
        .from("products")
        .select(
          `
          *,
          categories (
            id,
            name,
            description
          )
        `
        )
        .eq("is_active", true)
        .order("name");

      if (error) throw error;
      products.value = data || [];
    } catch (error) {
      console.error("Error fetching products:", error);
    } finally {
      loading.value = false;
    }
  };

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from("categories")
        .select("*")
        .order("name");

      if (error) throw error;
      categories.value = data || [];
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const getProductById = async (id: string): Promise<Product | null> => {
    try {
      const { data, error } = await supabase
        .from("products")
        .select(
          `
          *,
          categories (
            id,
            name,
            description
          )
        `
        )
        .eq("id", id)
        .eq("is_active", true)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error fetching product:", error);
      return null;
    }
  };

  const searchProducts = async (query: string) => {
    try {
      loading.value = true;
      searchQuery.value = query;

      if (!query.trim()) {
        await fetchProducts();
        return;
      }

      const { data, error } = await supabase
        .from("products")
        .select(
          `
          *,
          categories (
            id,
            name,
            description
          )
        `
        )
        .eq("is_active", true)
        .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
        .order("name");

      if (error) throw error;
      products.value = data || [];
    } catch (error) {
      console.error("Error searching products:", error);
    } finally {
      loading.value = false;
    }
  };

  const filterByCategory = (categoryId: string | null) => {
    selectedCategory.value = categoryId;
  };

  const clearFilters = () => {
    selectedCategory.value = null;
    searchQuery.value = "";
  };

  // Initialize store
  const initialize = async () => {
    await Promise.all([fetchProducts(), fetchCategories()]);
  };

  return {
    products: readonly(products),
    categories: readonly(categories),
    loading: readonly(loading),
    selectedCategory: readonly(selectedCategory),
    searchQuery: readonly(searchQuery),
    filteredProducts,
    productsByCategory,
    fetchProducts,
    fetchCategories,
    getProductById,
    searchProducts,
    filterByCategory,
    clearFilters,
    initialize,
  };
});
