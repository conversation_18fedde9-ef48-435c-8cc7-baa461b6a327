{"name": "vue-tsc", "version": "2.2.10", "license": "MIT", "files": ["bin", "**/*.js", "**/*.d.ts"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/tsc"}, "bin": {"vue-tsc": "./bin/vue-tsc.js"}, "peerDependencies": {"typescript": ">=5.0.0"}, "dependencies": {"@volar/typescript": "~2.4.11", "@vue/language-core": "2.2.10"}, "devDependencies": {"@types/node": "^22.10.4"}, "gitHead": "0422c03ffa4958431c9cd3cd19ae51f726c30b07"}