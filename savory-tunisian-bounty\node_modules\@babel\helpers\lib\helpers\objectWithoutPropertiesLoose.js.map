{"version": 3, "names": ["_objectWithoutPropertiesLoose", "source", "excluded", "target", "key", "Object", "prototype", "hasOwnProperty", "call", "indexOf"], "sources": ["../../src/helpers/objectWithoutPropertiesLoose.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _objectWithoutPropertiesLoose<\n  T extends object,\n  K extends PropertyKey[],\n>(\n  source: T | null | undefined,\n  excluded: K,\n): Pick<T, Exclude<keyof T, K[number]>>;\nexport default function _objectWithoutPropertiesLoose<\n  T extends object,\n  K extends Array<keyof T>,\n>(source: T | null | undefined, excluded: K): Omit<T, K[number]>;\nexport default function _objectWithoutPropertiesLoose<T extends object>(\n  source: T | null | undefined,\n  excluded: PropertyKey[],\n): Partial<T> {\n  if (source == null) return {};\n\n  var target: Partial<T> = {};\n\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) !== -1) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n"], "mappings": ";;;;;;AAae,SAASA,6BAA6BA,CACnDC,MAA4B,EAC5BC,QAAuB,EACX;EACZ,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAE7B,IAAIE,MAAkB,GAAG,CAAC,CAAC;EAE3B,KAAK,IAAIC,GAAG,IAAIH,MAAM,EAAE;IACtB,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,MAAM,EAAEG,GAAG,CAAC,EAAE;MACrD,IAAIF,QAAQ,CAACO,OAAO,CAACL,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAClCD,MAAM,CAACC,GAAG,CAAC,GAAGH,MAAM,CAACG,GAAG,CAAC;IAC3B;EACF;EAEA,OAAOD,MAAM;AACf", "ignoreList": []}